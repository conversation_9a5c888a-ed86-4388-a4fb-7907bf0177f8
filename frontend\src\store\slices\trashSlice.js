import { createSlice } from "@reduxjs/toolkit"

const initialState = {
  activeTab: "clusters",
  searchQuery: "",
  deletedClusters: [],
  deletedLinks: [],
  ui: {
    isDeleteModalOpen: false,
    itemToDelete: null,
  },
  restoringItems: [],
  deletingItems: [],
}

const trashSlice = createSlice({
  name: "trash",
  initialState,
  reducers: {
    setActiveTab: (state, action) => {
      state.activeTab = action.payload
    },
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload
    },
    openDeleteModal: (state, action) => {
      state.ui.isDeleteModalOpen = true
      state.ui.itemToDelete = action.payload
    },
    closeDeleteModal: (state) => {
      state.ui.isDeleteModalOpen = false
      state.ui.itemToDelete = null
    },
    clearSearchQuery: (state) => {
      state.searchQuery = ""
    },
    // Set deleted items from API
    setDeletedClusters: (state, action) => {
      state.deletedClusters = action.payload
    },
    setDeletedLinks: (state, action) => {
      state.deletedLinks = action.payload
    },
    // Add items to trash when deleted from main pages
    clusterMovedToTrash: (state, action) => {
      const cluster = action.payload
      // Check if cluster is not already in trash
      const existingIndex = state.deletedClusters.findIndex((c) => c.id === cluster.id)
      if (existingIndex === -1) {
        state.deletedClusters.push(cluster)
      }
    },
    linkMovedToTrash: (state, action) => {
      const link = action.payload
      // Check if link is not already in trash
      const existingIndex = state.deletedLinks.findIndex((l) => l.id === link.id)
      if (existingIndex === -1) {
        state.deletedLinks.push(link)
      }
    },
    // Remove items from trash when restored
    clusterRestoredFromTrash: (state, action) => {
      const clusterId = action.payload
      state.deletedClusters = state.deletedClusters.filter((cluster) => cluster.id !== clusterId)
    },
    linkRestoredFromTrash: (state, action) => {
      const linkId = action.payload
      state.deletedLinks = state.deletedLinks.filter((link) => link.id !== linkId)
    },
    // Remove items from trash when permanently deleted
    clusterPermanentlyDeleted: (state, action) => {
      const clusterId = action.payload
      state.deletedClusters = state.deletedClusters.filter((cluster) => cluster.id !== clusterId)
    },
    linkPermanentlyDeleted: (state, action) => {
      const linkId = action.payload
      state.deletedLinks = state.deletedLinks.filter((link) => link.id !== linkId)
    },
    // Loading states
    addRestoringItem: (state, action) => {
      const itemId = action.payload
      if (!state.restoringItems.includes(itemId)) {
        state.restoringItems.push(itemId)
      }
    },
    removeRestoringItem: (state, action) => {
      const itemId = action.payload
      state.restoringItems = state.restoringItems.filter((id) => id !== itemId)
    },
    addDeletingItem: (state, action) => {
      const itemId = action.payload
      if (!state.deletingItems.includes(itemId)) {
        state.deletingItems.push(itemId)
      }
    },
    removeDeletingItem: (state, action) => {
      const itemId = action.payload
      state.deletingItems = state.deletingItems.filter((id) => id !== itemId)
    },
    // Reset state
    resetTrashState: () => initialState
  },
})

export const {
  setActiveTab,
  setSearchQuery,
  openDeleteModal,
  closeDeleteModal,
  clearSearchQuery,
  setDeletedClusters,
  setDeletedLinks,
  clusterMovedToTrash,
  linkMovedToTrash,
  clusterRestoredFromTrash,
  linkRestoredFromTrash,
  clusterPermanentlyDeleted,
  linkPermanentlyDeleted,
  addRestoringItem,
  removeRestoringItem,
  addDeletingItem,
  removeDeletingItem,
  resetTrashState
} = trashSlice.actions

export default trashSlice.reducer
