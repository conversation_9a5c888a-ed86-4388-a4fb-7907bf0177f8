import { createSlice } from "@reduxjs/toolkit"

// Define navigation types
export const NAVIGATION_TYPES = {
  INTRA_WORKSPACE: "intra-workspace",
  INTER_WORKSPACE: "inter-workspace",
  EXTERNAL: "external"
}

// Define navigation pages
export const NAVIGATION_PAGES = {
  OVERVIEW: "overview",
  CLUSTERS: "clusters",
  TRASH: "trash",
  SETTINGS: "settings"
}

// Helper function to determine page from pathname
export const getPageFromPath = (pathname) => {
  if (pathname.includes("/trash")) return NAVIGATION_PAGES.TRASH
  if (pathname.includes("/settings")) return NAVIGATION_PAGES.SETTINGS
  if (pathname.includes("/clusters")) return NAVIGATION_PAGES.CLUSTERS
  if (pathname.match(/^\/workspace\/[^/]+\/?$/)) return NAVIGATION_PAGES.OVERVIEW
  return NAVIGATION_PAGES.OVERVIEW // Default fallback
}

// Helper function to build route for a page
export const buildRoute = (page, workspaceId, additionalPath = "") => {
  if (!workspaceId) return "/"
  
  const baseRoutes = {
    [NAVIGATION_PAGES.OVERVIEW]: `/workspace/${workspaceId}`,
    [NAVIGATION_PAGES.CLUSTERS]: `/workspace/${workspaceId}/clusters`,
    [NAVIGATION_PAGES.TRASH]: `/workspace/${workspaceId}/trash`,
    [NAVIGATION_PAGES.SETTINGS]: `/workspace/${workspaceId}/settings`,
  }
  
  const basePath = baseRoutes[page] || baseRoutes[NAVIGATION_PAGES.OVERVIEW]
  return basePath + (additionalPath || "")
}

const initialState = {
  // Current navigation state
  isNavigating: false,
  navigationType: null,
  currentPage: NAVIGATION_PAGES.OVERVIEW,

  // Page-specific states
  pageStates: {
    [NAVIGATION_PAGES.OVERVIEW]: {},
    [NAVIGATION_PAGES.CLUSTERS]: {
      searchQuery: "",
      selectedCluster: null
    },
    [NAVIGATION_PAGES.TRASH]: {
      activeTab: "clusters",
      searchQuery: ""
    },
    [NAVIGATION_PAGES.SETTINGS]: {}
  }
}

const navigationSlice = createSlice({
  name: "navigation",
  initialState,
  reducers: {
    // Set navigation state
    setNavigationState: (state, action) => {
      const { isNavigating, navigationType } = action.payload
      state.isNavigating = isNavigating
      if (navigationType) {
        state.navigationType = navigationType
      }
    },

    // Set current page
    setCurrentPage: (state, action) => {
      state.currentPage = action.payload
    },

    // Update page state
    updatePageState: (state, action) => {
      const { page, updates } = action.payload
      if (state.pageStates[page]) {
        state.pageStates[page] = {
          ...state.pageStates[page],
          ...updates
        }
      }
    },

    // Reset page state
    resetPageState: (state, action) => {
      const { page } = action.payload
      if (state.pageStates[page]) {
        state.pageStates[page] = initialState.pageStates[page]
      }
    },

    // Initialize workspace navigation
    initializeWorkspaceNavigation: (state, action) => {
      const { workspaceId, currentPath } = action.payload
      const page = getPageFromPath(currentPath)
      state.currentPage = page
    },

    // Handle workspace switch
    handleWorkspaceSwitch: (state, action) => {
      // Reset navigation state for new workspace
      state.isNavigating = true
      state.navigationType = NAVIGATION_TYPES.INTER_WORKSPACE
      state.currentPage = NAVIGATION_PAGES.OVERVIEW

      // Reset all page states
      Object.keys(state.pageStates).forEach(page => {
        state.pageStates[page] = initialState.pageStates[page]
      })
    },

    // Reset navigation state
    resetNavigationState: () => initialState,

    // Reset all navigation
    resetNavigation: () => initialState
  }
})

export const {
  setNavigationState,
  setCurrentPage,
  updatePageState,
  resetPageState,
  initializeWorkspaceNavigation,
  handleWorkspaceSwitch,
  resetNavigationState,
  resetNavigation
} = navigationSlice.actions

// Selectors
export const selectNavigationState = (state) => ({
  isNavigating: state.navigation.isNavigating,
  navigationType: state.navigation.navigationType
})

export const selectCurrentPage = (state) => state.navigation.currentPage
export const selectIsNavigating = (state) => state.navigation.isNavigating
export const selectNavigationType = (state) => state.navigation.navigationType

export const selectPageState = (page) => (state) => 
  state.navigation.pageStates[page] || {}

export default navigationSlice.reducer
