import { useSelector } from "react-redux"
import {
  useGetDeletedClustersQuery,
  useGetDeletedLinksQuery,
  useRestoreClusterMutation,
  useRestoreLinkMutation,
  usePermanentlyDeleteClusterMutation,
  usePermanentlyDeleteLinkMutation,
} from "../store/api/trashApi"
import { useNavigation } from "./useNavigation"

export const useTrash = (workspaceId) => {
  // Get navigation state to track workspace changes
  const { isNavigating, navigationType } = useNavigation()
  
  // Get data from Redux store instead of RTK Query cache
  const { deletedClusters, deletedLinks, searchQuery, restoringItems, deletingItems } = useSelector(
    (state) => state.trash,
  )

  // Still use RTK Query for API calls, but data comes from Redux store
  const { isLoading: isFetchingClusters, error: clustersError, refetch: refetchClusters } = useGetDeletedClustersQuery(workspaceId, {
    // Only skip if no workspaceId, allow during navigation to ensure data is fetched
    skip: !workspaceId,
    // Force refetch when component mounts or workspaceId changes
    refetchOnMountOrArgChange: true
  })

  const { isLoading: isFetchingLinks, error: linksError, refetch: refetchLinks } = useGetDeletedLinksQuery(workspaceId, {
    // Only skip if no workspaceId, allow during navigation to ensure data is fetched
    skip: !workspaceId,
    // Force refetch when component mounts or workspaceId changes
    refetchOnMountOrArgChange: true
  })

  // Log state for debugging
  console.log("🗑️ Trash Hook State:", {
    workspaceId,
    isNavigating,
    navigationType,
    isFetchingClusters,
    isFetchingLinks,
    clustersCount: deletedClusters.length,
    linksCount: deletedLinks.length
  })

  const [restoreCluster] = useRestoreClusterMutation()
  const [restoreLink] = useRestoreLinkMutation()
  const [permanentlyDeleteCluster] = usePermanentlyDeleteClusterMutation()
  const [permanentlyDeleteLink] = usePermanentlyDeleteLinkMutation()

  // Filter items based on search query and current workspace
  const filteredClusters = deletedClusters.filter((cluster) =>
    cluster.name?.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const filteredLinks = deletedLinks.filter((link) => link.url?.toLowerCase().includes(searchQuery.toLowerCase()))

  const handleRestoreCluster = async (clusterId) => {
    try {
      await restoreCluster({ clusterId, workspaceId }).unwrap()
      return true
    } catch (error) {
      console.error("Error restoring cluster:", error)
      return false
    }
  }

  const handleRestoreLink = async (linkId) => {
    try {
      // Find the link to get cluster ID
      const link = deletedLinks.find((l) => l.id === linkId)
      if (!link) return false

      await restoreLink({ clusterId: link.clusterId, linkId, workspaceId }).unwrap()
      return true
    } catch (error) {
      console.error("Error restoring link:", error)
      return false
    }
  }

  const handlePermanentlyDeleteCluster = async (clusterId) => {
    try {
      await permanentlyDeleteCluster({ clusterId, workspaceId }).unwrap()
      return true
    } catch (error) {
      console.error("Error permanently deleting cluster:", error)
      return false
    }
  }

  const handlePermanentlyDeleteLink = async (linkId) => {
    try {
      // Find the link to get cluster ID
      const link = deletedLinks.find((l) => l.id === linkId)
      if (!link) return false

      await permanentlyDeleteLink({ clusterId: link.clusterId, linkId, workspaceId }).unwrap()
      return true
    } catch (error) {
      console.error("Error permanently deleting link:", error)
      return false
    }
  }

  const isItemRestoring = (itemId) => {
    return restoringItems.includes(itemId)
  }

  const isItemDeleting = (itemId) => {
    return deletingItems.includes(itemId)
  }

  // Manual refetch function for debugging or error recovery
  const handleManualRefresh = async () => {
    console.log("🔄 Manually refreshing trash data...")
    try {
      await Promise.all([
        refetchClusters(),
        refetchLinks()
      ])
      console.log("✅ Trash data refreshed successfully")
    } catch (error) {
      console.error("❌ Error refreshing trash data:", error)
    }
  }

  return {
    clusters: filteredClusters,
    links: filteredLinks,
    isLoading: isFetchingClusters || isFetchingLinks || isNavigating,
    error: clustersError || linksError,
    handleRestoreCluster,
    handleRestoreLink,
    handlePermanentlyDeleteCluster,
    handlePermanentlyDeleteLink,
    handleManualRefresh,
    totalClusters: deletedClusters.length,
    totalLinks: deletedLinks.length,
    filteredClustersCount: filteredClusters.length,
    filteredLinksCount: filteredLinks.length,
    isItemRestoring,
    isItemDeleting,
  }
}
