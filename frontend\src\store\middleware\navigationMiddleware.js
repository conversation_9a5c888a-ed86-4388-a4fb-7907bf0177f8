import { setNavigationState } from "../slices/navigationSlice"
import { resetTrashState } from "../slices/trashSlice"

/**
 * Navigation middleware to coordinate navigation state with workspace management
 */
const navigationMiddleware = (store) => (next) => (action) => {
  // Handle workspace changes
  if (action.type === "workspace/setCurrentWorkspace") {
    // Reset trash state when workspace changes
    store.dispatch(resetTrashState())
  }

  // Handle navigation state
  if (action.type === "navigation/setNavigationState") {
    const { isNavigating, navigationType } = action.payload
    
    // If starting navigation
    if (isNavigating && navigationType === "WORKSPACE_SWITCH") {
      // Reset trash state at the start of workspace switch
      store.dispatch(resetTrashState())
    }
  }

  return next(action)
}

export default navigationMiddleware
