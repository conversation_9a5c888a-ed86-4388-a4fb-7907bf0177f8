import { useEffect, useState } from "react"
import { Outlet, useLocation } from "react-router-dom"
import { useSelector } from "react-redux"
import { useNavigation } from "../hooks/useNavigation"
import { useWorkspaceManager } from "./common/WorkspaceManager"
import { selectWorkspaceLoading } from "../store/slices/workspaceSlice"
import OnboardingCheck from "./onboarding-check"

const WorkspaceLayout = () => {
  const location = useLocation()
  const [hasInitialized, setHasInitialized] = useState(false)

  // Get workspace manager and navigation functions
  const { currentWorkspace } = useWorkspaceManager()
  const { initializeNavigation, syncWithCurrentUrl } = useNavigation()

  // Get loading state from Redux
  const { workspaces: isWorkspacesLoading } = useSelector(selectWorkspaceLoading)

  // Initialize navigation state when workspace changes
  useEffect(() => {
    if (currentWorkspace && !hasInitialized) {
      initializeNavigation(currentWorkspace, location.pathname)
      setHasInitialized(true)
    }
  }, [currentWorkspace, location.pathname, initializeNavigation, hasInitialized])

  // Sync navigation state with URL changes
  useEffect(() => {
    if (currentWorkspace) {
      syncWithCurrentUrl()
    }
  }, [location.pathname, syncWithCurrentUrl, currentWorkspace])

  // Show loading while workspace is being loaded
  if (isWorkspacesLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#352090] mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading workspace...</p>
        </div>
      </div>
    )
  }

  // If no workspace is available, show onboarding
  if (!currentWorkspace) {
    return <OnboardingCheck />
  }

  // Render the workspace content only when we have a valid workspace
  return <Outlet />
}

export default WorkspaceLayout
