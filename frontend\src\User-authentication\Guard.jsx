import { useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import { useValidateSessionQuery } from "../store/api/authApi"
import { logout } from "../store/slices/authSlice"
import { addNotification } from "../store/slices/uiSlice"

const AdminGuard = ({ children }) => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { isAuthenticated } = useSelector((state) => state.auth)

  const {
    data: sessionData,
    isLoading,
    error,
  } = useValidateSessionQuery(undefined, {
    skip: !isAuthenticated,
  })

  useEffect(() => {
    if (error) {
      console.error("Session validation failed:", error)

      dispatch(
        addNotification({
          type: "error",
          title: "Session Invalid",
          message: "Your session has expired. Please log in again.",
        }),
      )

      dispatch(logout())
      navigate("/login")
      return
    }

    if (sessionData) {
      // Check if user has admin role
      if (sessionData.role !== "admin") {
        dispatch(
          addNotification({
            type: "error",
            title: "Access Denied",
            message: "You don't have permission to access this page. Admin access required.",
          }),
        )

        // Redirect to appropriate page based on user role
        navigate("/dashboard") // or wherever non-admin users should go
        return
      }
    }
  }, [sessionData, error, dispatch, navigate])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        <span className="ml-3 text-gray-600">Validating session...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">Access Denied</h3>
          <p className="mt-2 text-gray-600">Session validation failed</p>
        </div>
      </div>
    )
  }

  // If we have valid session data and user is admin, render children
  if (sessionData && sessionData.role === "admin") {
    return (
      children || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="mt-2 text-gray-600">Welcome, Admin!</p>
          </div>
        </div>
      )
    )
  }

  return null
}

export default AdminGuard
