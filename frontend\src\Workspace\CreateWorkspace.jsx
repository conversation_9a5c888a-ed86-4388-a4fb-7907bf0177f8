import { useState } from "react"
import { useSelector } from "react-redux"
import Navbar from "../components/navbar"
import CreateWorkspaceModal from "./create-workspace-modal"
import { useWorkspaceManager } from "../components/common/WorkspaceManager"
import { useGetWorkspaceMembersBatchQuery } from "../store/api/teamApi"
import { 
  selectWorkspaces, 
  selectIsCreating, 
  selectIsSwitching, 
  selectWorkspaceLoading 
} from "../store/slices/workspaceSlice"
import userAvatar from "/image.svg"

const CreateWorkspace = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Get workspace data and state from Redux
  const workspaces = useSelector(selectWorkspaces)
  const isCreating = useSelector(selectIsCreating)
  const isSwitching = useSelector(selectIsSwitching)
  const { workspaces: isWorkspacesLoading } = useSelector(selectWorkspaceLoading)

  // Get workspace manager functions
  const { createWorkspace, getWorkspaceDomainInfo, switchWorkspace } = useWorkspaceManager()

  // Get workspace members data
  const { data: workspaceMembersData } = useGetWorkspaceMembersBatchQuery(
    { workspace_ids: workspaces.map(ws => ws.id || ws._id) },
    { skip: workspaces.length === 0 }
  )

  const handleCreateWorkspace = () => {
    setIsModalOpen(true)
  }

  const handleModalSubmit = async (workspaceName) => {
    try {
      await createWorkspace(workspaceName)
      return true
    } catch (error) {
      console.error("Error creating workspace:", error)
      return false
    }
  }

  const handleModalClose = () => {
    setIsModalOpen(false)
  }

  const handleSelectWorkspace = async (workspaceId) => {
    if (isSwitching) return
    
    try {
      await switchWorkspace(workspaceId)
    } catch (error) {
      console.error("Error selecting workspace:", error)
    }
  }

  const isLoading = isWorkspacesLoading || isCreating || isSwitching

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="fixed top-0 left-0 right-0 h-16 bg-white z-30">
          <Navbar />
        </div>
        <main className="flex-1 px-6 py-8 pt-[100px] md:ml-[200px] overflow-hidden" style={{ minHeight: "calc(100vh - 100px" }}>
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#352090] mx-auto"></div>
            <p className="mt-4 text-gray-600">
              {isCreating ? "Creating workspace..." : "Loading workspaces..."}
            </p>
            {(isCreating || isSwitching) && (
              <p className="mt-2 text-sm text-gray-500">
                This may take a few moments. Please don't close this window.
              </p>
            )}
          </div>
        </main>
      </div>
    )
  }

  // No workspaces state - show create button
  if (workspaces.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="fixed top-0 left-0 right-0 h-16 bg-white z-30">
          <Navbar />
        </div>
        <main className="flex-1 p-6 pt-[100px] mt-6 md:ml-[200px] overflow-auto" style={{ minHeight: "calc(100vh - 100px)" }}>
          <div className="text-center">
            <div className="flex items-center justify-center">
              <button
                onClick={handleCreateWorkspace}
                className="flex items-center gap-[9px] cursor-pointer transition-opacity duration-200 focus:outline-none focus:ring-2 focus:ring-[#352090] focus:ring-offset-2 rounded-lg hover:opacity-80"
              >
                <svg
                  width="45"
                  height="45"
                  viewBox="0 0 45 45"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="flex-shrink-0"
                >
                  <rect width="45" height="45" rx="14" fill="#E4EEFF" />
                  <path
                    d="M22.5 17.5417V27.4584M17.5417 22.5001H27.4584"
                    stroke="#352090"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span className="text-[#352090] text-[14px] leading-[100%] tracking-[0%] whitespace-nowrap font-['Poppins']">
                  Create New Workspace
                </span>
              </button>
            </div>
          </div>
        </main>
        <CreateWorkspaceModal isOpen={isModalOpen} onClose={handleModalClose} onSubmit={handleModalSubmit} />
      </div>
    )
  }

  // Workspaces exist - show workspace selection interface
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="flex-1 p-6 pt-[100px] mt-6 md:ml-[200px] overflow-auto" style={{ minHeight: "calc(100vh - 100px)" }}>
        <div className="max-w-4xl mx-auto h-full">
          {/* Header section */}
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-lg font-medium text-gray-900">Select your workspace to continue:</h1>
            <button
              onClick={handleCreateWorkspace}
              className="flex items-center gap-[9px] cursor-pointer transition-opacity duration-200 focus:outline-none focus:ring-2 focus:ring-[#352090] focus:ring-offset-2 rounded-lg hover:opacity-80"
            >
              <svg
                width="45"
                height="45"
                viewBox="0 0 45 45"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="flex-shrink-0"
              >
                <rect width="45" height="45" rx="14" fill="#E4EEFF" />
                <path
                  d="M22.5 17.5417V27.4584M17.5417 22.5001H27.4584"
                  stroke="#352090"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <span className="text-[#352090] text-[14px] leading-[100%] tracking-[0%] whitespace-nowrap font-['Poppins']">
                Create New Workspace
              </span>
            </button>
          </div>

          {/* Workspace List */}
          <div className="overflow-y-auto max-h-[calc(100vh-250px)] pr-2 workspace-scroll">
            <div className="flex flex-col gap-4">
              {workspaces.map((workspace) => {
                const id = workspace.id || workspace._id
                const domain = getWorkspaceDomainInfo(workspace)
                const members = workspaceMembersData?.data?.[id] || []

                return (
                  <div
                    key={id}
                    className="bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 w-full min-w-[400px] max-w-2xl mx-auto"
                  >
                    <div className="flex items-center justify-between">
                      {/* Workspace Info */}
                      <div className="flex flex-col gap-2">
                        <h3 className="text-lg font-medium text-gray-900">{workspace.name || id}</h3>
                        <div className="flex flex-col gap-2">
                          <span className="text-sm text-gray-500">{domain}</span>
                          <div className="flex items-center gap-2">
                            <div className="flex -space-x-2">
                              {members.slice(0, 5).map((member, index) => (
                                <img
                                  key={member.email}
                                  src={userAvatar}
                                  alt={member.full_name || member.email}
                                  className="w-6 h-6 rounded-full border-2 border-white"
                                  style={{ zIndex: 5 - index }}
                                />
                              ))}
                              {members.length > 5 && (
                                <div className="w-6 h-6 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-xs text-gray-600" style={{ zIndex: 0 }}>
                                  +{members.length - 5}
                                </div>
                              )}
                            </div>
                            <span className="text-sm text-gray-500">
                              {members.length} member{members.length !== 1 ? "s" : ""}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Select Button */}
                      <button
                        onClick={() => handleSelectWorkspace(id)}
                        disabled={isSwitching}
                        className={`px-6 py-2 bg-[#352090] text-white font-medium text-sm rounded-xl transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#352090] focus:ring-offset-2
                          ${isSwitching ? "opacity-50 cursor-not-allowed" : "hover:bg-[#2a1a70]"}`}
                      >
                        Select
                      </button>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </main>
      <CreateWorkspaceModal isOpen={isModalOpen} onClose={handleModalClose} onSubmit={handleModalSubmit} />
    </div>
  )
}

export default CreateWorkspace
