import { createSlice } from "@reduxjs/toolkit"

const initialState = {
  searchQuery: "",
  ui: {
    isCreateModalOpen: false,
    isDeleteModalOpen: false,
    clusterToDelete: null
  },
  // Workspace-specific state management
  clustersByWorkspace: {},
  // Processing clusters state
  processingClusters: [],
  // Filter state
  filters: {
    device: "all",
    country: "all"
  }
}

const clusterSlice = createSlice({
  name: "cluster",
  initialState,
  reducers: {
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload
    },
    openCreateModal: (state) => {
      state.ui.isCreateModalOpen = true
    },
    closeCreateModal: (state) => {
      state.ui.isCreateModalOpen = false
    },
    openDeleteModal: (state, action) => {
      state.ui.isDeleteModalOpen = true
      state.ui.clusterToDelete = action.payload
    },
    closeDeleteModal: (state) => {
      state.ui.isDeleteModalOpen = false
      state.ui.clusterToDelete = null
    },

    // Filter actions
    setFilters: (state, action) => {
      state.filters = {
        ...state.filters,
        ...action.payload
      }
    },
    resetFilters: (state) => {
      state.filters = initialState.filters
    },

    // Workspace-specific actions
    setWorkspaceClusters: (state, action) => {
      const { workspaceId, clusters } = action.payload
      state.clustersByWorkspace[workspaceId] = clusters
    },
    addWorkspaceCluster: (state, action) => {
      const { workspaceId, cluster } = action.payload
      if (!state.clustersByWorkspace[workspaceId]) {
        state.clustersByWorkspace[workspaceId] = []
      }
      state.clustersByWorkspace[workspaceId].push(cluster)
    },
    removeWorkspaceCluster: (state, action) => {
      const { workspaceId, clusterId } = action.payload
      if (state.clustersByWorkspace[workspaceId]) {
        state.clustersByWorkspace[workspaceId] = state.clustersByWorkspace[workspaceId].filter(
          (cluster) => cluster._id !== clusterId
        )
      }
    },
    updateWorkspaceCluster: (state, action) => {
      const { workspaceId, clusterId, updates } = action.payload
      if (state.clustersByWorkspace[workspaceId]) {
        const index = state.clustersByWorkspace[workspaceId].findIndex(
          (cluster) => cluster._id === clusterId
        )
        if (index !== -1) {
          state.clustersByWorkspace[workspaceId][index] = {
            ...state.clustersByWorkspace[workspaceId][index],
            ...updates,
          }
        }
      }
    },
    clearWorkspaceClusters: (state, action) => {
      const workspaceId = action.payload
      delete state.clustersByWorkspace[workspaceId]
    },

    // Processing clusters actions
    addProcessingCluster: (state, action) => {
      const clusterId = action.payload
      if (!state.processingClusters.includes(clusterId)) {
        state.processingClusters.push(clusterId)
      }
    },
    removeProcessingCluster: (state, action) => {
      const clusterId = action.payload
      state.processingClusters = state.processingClusters.filter((id) => id !== clusterId)
    },
    clearAllProcessingClusters: (state) => {
      state.processingClusters = []
    },

    resetClusterState: () => initialState
  }
})

export const {
  setSearchQuery,
  openCreateModal,
  closeCreateModal,
  openDeleteModal,
  closeDeleteModal,
  // Filter actions
  setFilters,
  resetFilters,
  // Workspace cluster actions
  setWorkspaceClusters,
  addWorkspaceCluster,
  removeWorkspaceCluster,
  updateWorkspaceCluster,
  clearWorkspaceClusters,
  // Processing cluster actions
  addProcessingCluster,
  removeProcessingCluster,
  clearAllProcessingClusters,
  // Reset action
  resetClusterState
} = clusterSlice.actions

// Selectors
export const selectClustersByWorkspace = (state) => state.cluster.clustersByWorkspace
export const selectProcessingClusters = (state) => state.cluster.processingClusters
export const selectClusterUI = (state) => state.cluster.ui
export const selectSearchQuery = (state) => state.cluster.searchQuery
export const selectClusterFilters = (state) => state.cluster.filters
export const selectClusterSearchQuery = (state) => state.cluster.searchQuery

export default clusterSlice.reducer
