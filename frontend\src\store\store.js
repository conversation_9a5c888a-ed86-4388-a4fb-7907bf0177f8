import { configureStore } from "@reduxjs/toolkit"
import { setupListeners } from "@reduxjs/toolkit/query"
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from "redux-persist"
import storage from "redux-persist/lib/storage"

// Reducers
import workspaceReducer from "./slices/workspaceSlice"
import navigationReducer from "./slices/navigationSlice"
import authReducer from "./slices/authSlice"
import uiReducer from "./slices/uiSlice"
import onboardingReducer from "./slices/onboardingSlice"
import clusterReducer from "./slices/clusterSlice"
import trashReducer from "./slices/trashSlice"
import linkReducer from "./slices/linkSlice"

// API
import { apiSlice } from "./api/apiSlice"

// Middleware
import navigationMiddleware from "./middleware/navigationMiddleware"

// Persist Config
const workspacePersistConfig = {
  key: "workspace",
  storage,
  whitelist: ["workspaces"] // Only persist workspaces array
}

const navigationPersistConfig = {
  key: "navigation",
  storage,
  whitelist: ["pageStates"] // Only persist page-specific states
}

const authPersistConfig = {
  key: "auth",
  storage,
  whitelist: ["isAuthenticated", "user"] // Only persist auth status and user data
}

const onboardingPersistConfig = {
  key: "onboarding",
  storage,
  whitelist: ["workspaceStates"] // Only persist workspace onboarding states
}

const trashPersistConfig = {
  key: "trash",
  storage,
  whitelist: ["deletedClusters", "deletedLinks"] // Only persist deleted items
}

const linkPersistConfig = {
  key: "link",
  storage,
  whitelist: ["activeLinks"] // Only persist active links
}

// Create Store
export const store = configureStore({
  reducer: {
    workspace: persistReducer(workspacePersistConfig, workspaceReducer),
    navigation: persistReducer(navigationPersistConfig, navigationReducer),
    auth: persistReducer(authPersistConfig, authReducer),
    onboarding: persistReducer(onboardingPersistConfig, onboardingReducer),
    trash: persistReducer(trashPersistConfig, trashReducer),
    link: persistReducer(linkPersistConfig, linkReducer),
    cluster: clusterReducer,
    ui: uiReducer, // UI state doesn't need persistence
    [apiSlice.reducerPath]: apiSlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    })
      .concat(apiSlice.middleware)
      .concat(navigationMiddleware),
  devTools: process.env.NODE_ENV !== "production",
})

// Enable RTK Query cache listeners
setupListeners(store.dispatch)

// Create persistor
export const persistor = persistStore(store)

// Reset store function
export const resetStore = async () => {
  // Clear persisted state
  await persistor.purge()
  
  // Reset reducers to initial state
  store.dispatch({ type: "workspace/resetWorkspaceState" })
  store.dispatch({ type: "navigation/resetNavigation" })
  store.dispatch({ type: "auth/logout" })
  store.dispatch({ type: "ui/resetUI" })
  store.dispatch({ type: "onboarding/resetOnboarding" })
  store.dispatch({ type: "cluster/resetClusterState" })
  store.dispatch({ type: "trash/resetTrashState" })
  store.dispatch({ type: "link/resetLinkState" })
  
  // Clear API cache
  store.dispatch(apiSlice.util.resetApiState())
}

// Selector to check if store is rehydrated
export const selectIsRehydrated = (state) => 
  state._persist?.rehydrated === true

export default store
