import { useState, useEffect } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { useDispatch } from "react-redux"
import { X } from "lucide-react"
import { useWorkspaceManager } from "./common/WorkspaceManager"
import { useNavigation } from "../hooks/useNavigation"
import { addNotification } from "../store/slices/uiSlice"
import { useLogoutMutation } from "../store/api/authApi"
import ModalPortal from "./ModalPortal"
import InviteUserModal from "../team_invitation/invite-user"

const Sidebar = ({ activePage = "overview" }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()
  
  // Workspace & navigation hooks
  const { currentWorkspace } = useWorkspaceManager()
  const {
    currentPage,
    isNavigating,
    navigationType,
    navigateToClusters,
    navigateToTrash,
    navigateToSettings,
    syncWithCurrentUrl,
    isPageActive,
    PAGES
  } = useNavigation()
  
  // UI state
  const [isInviteModalOpen, setInviteModalOpen] = useState(false)
  const [isLogoutModalOpen, setLogoutModalOpen] = useState(false)
  
  // Logout mutation
  const [logout, { isLoading, isSuccess, isError, error }] = useLogoutMutation()

  // Sync nav on URL change
  useEffect(() => {
    syncWithCurrentUrl()
  }, [location.pathname, syncWithCurrentUrl])

  // Handle logout result
  useEffect(() => {
    if (isSuccess) {
      dispatch(addNotification({
        type: "success",
        title: "Logged Out",
        message: "You have been logged out successfully",
      }))
      navigate("/login", { replace: true })
    }
    if (isError) {
      dispatch(addNotification({
        type: "error",
        title: "Logout Failed",
        message: error?.data?.message || "Failed to log out; local session cleared.",
      }))
      navigate("/login", { replace: true })
    }
  }, [isSuccess, isError, error, dispatch, navigate])

  const openInviteModal = () => setInviteModalOpen(true)
  const closeInviteModal = () => setInviteModalOpen(false)
  const openLogoutModal = () => setLogoutModalOpen(true)
  const closeLogoutModal = () => setLogoutModalOpen(false)

  // **Your original optimized navigation handler** with console logs and Redux guard
  const handleNavigation = (id) => {
    console.log("🚀 Sidebar Navigation Debug:", {
      id,
      currentWorkspace,
      currentPath: location.pathname,
      navigationHookState: { currentPage, isNavigating, navigationType }
    })

    if (!currentWorkspace) {
      console.error("❌ Sidebar Navigation Error: No workspace ID available", {
        currentWorkspace,
        location: location.pathname
      })
      return
    }

    switch (id) {
      case "overview":
        console.log("🔄 Sidebar: Calling navigateToClusters()")
        navigateToClusters()
        break
      case "trash":
        console.log("🔄 Sidebar: Calling navigateToTrash()", {
          currentWorkspace,
          currentPath: location.pathname,
          navigateToTrash: typeof navigateToTrash
        })
        navigateToTrash()
        break
      case "settings":
        console.log("🔄 Sidebar: Calling navigateToSettings()")
        navigateToSettings()
        break
      default:
        console.warn("Unknown navigation id:", id)
    }
  }

  const handleLogout = () => {
    setLogoutModalOpen(false)
    logout()
  }

  const menuItems = [
    { id: "overview",  label: "Overview", icon: "/Overview.svg" },
    { id: "trash",     label: "Trash",    icon: "/Trash2.svg"   },
    { id: "settings",  label: "Settings", icon: "/setting.svg"  },
    { id: "logout",    label: "Logout",   icon: "/Logout.svg", onClick: openLogoutModal },
  ]

  const isMenuItemActive = (itemId) =>
    isPageActive(PAGES[itemId.toUpperCase()]) || activePage === itemId

  return (
    <>
      <aside className="w-[200px] border-r border-gray-200 h-[calc(100vh-100px)] bg-white fixed top-[100px] left-0 z-20 flex flex-col">
        <nav className="flex-1">
          <ul className="space-y-1">
            {menuItems.map(item => {
              const isActive = isMenuItemActive(item.id)
              return (
                <li key={item.id}>
                  <button
                    onClick={item.onClick ?? (() => handleNavigation(item.id))}
                    type="button"
                    aria-current={isActive ? "page" : undefined}
                    aria-label={`Navigate to ${item.label}`}
                    className={`flex items-center px-6 py-3 w-full text-left text-sm transition-colors duration-200 ${
                      isActive
                        ? "text-[#352090] bg-[#F8F7FC] border-l-4 border-[#DCDCDC] pl-5"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                    }`}
                  >
                    <img
                      src={item.icon}
                      alt=""
                      aria-hidden="true"
                      className={`w-5 h-5 mr-3 transition-all ${
                        isActive ? "filter-purple" : ""
                      }`}
                      style={{
                        filter: isActive
                          ? "invert(47%) sepia(92%) saturate(1466%) hue-rotate(233deg) brightness(99%) contrast(101%)"
                          : "none",
                      }}
                    />
                    {item.label}
                  </button>
                </li>
              )
            })}
          </ul>
        </nav>

        {currentWorkspace && (
          <div className="p-4">
            <button
              onClick={openInviteModal}
              className="w-full h-[44px] flex items-center justify-center text-sm text-[#352090] border border-[#ECE8FF] rounded-[8px] hover:bg-[#F8F7FC] transition-colors"
            >
              <img src="/Team_member.svg" alt="Invite Team" className="w-4 h-4 mr-2" />
              Invite Team
            </button>
          </div>
        )}
      </aside>

      {/* Invite User Modal */}
      {isInviteModalOpen && (
        <ModalPortal>
          <InviteUserModal
            isOpen={isInviteModalOpen}
            onClose={closeInviteModal}
            workspaceId={currentWorkspace}
          />
        </ModalPortal>
      )}

      {/* Logout Confirmation Modal */}
      {isLogoutModalOpen && (
        <ModalPortal>
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Confirm Logout</h3>
                <button onClick={closeLogoutModal} className="text-gray-400 hover:text-gray-500">
                  <X className="w-5 h-5" />
                </button>
              </div>
              <p className="text-gray-600 mb-6">
                Are you sure you want to logout? You will need to login again to access your
                account.
              </p>
              <div className="flex justify-end gap-3">
                <button
                  onClick={closeLogoutModal}
                  disabled={isLoading}
                  className="px-4 py-2 text-gray-700 hover:text-gray-900 font-medium"
                >
                  Cancel
                </button>
                <button
                  onClick={handleLogout}
                  disabled={isLoading}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 font-medium disabled:opacity-50"
                >
                  {isLoading ? "Logging out..." : "Logout"}
                </button>
              </div>
            </div>
          </div>
        </ModalPortal>
      )}
    </>
  )
}

export default Sidebar
