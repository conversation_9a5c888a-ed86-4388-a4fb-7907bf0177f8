import { useState, useEffect, useRef } from "react"
import { useNavigate, useSearchParams } from "react-router-dom"
import { useDispatch } from "react-redux"
import { useVerifyEmailMutation } from "../store/api/authApi"
import { addNotification } from "../store/slices/uiSlice"
import LogoComponent from "../components/logo-component"
import GlowLoading from "/Glow loading.gif"

const VerifyEmail = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const [searchParams] = useSearchParams()

  // 1) Pull token into a primitive
  const token = searchParams.get("token")

  const [verificationStatus, setVerificationStatus] = useState("verifying")
  const [verifyEmail, { isLoading }] = useVerifyEmailMutation()

  // 2) Guard so effect only runs once
  const hasRun = useRef(false)

  useEffect(() => {
    if (hasRun.current) return
    hasRun.current = true

    const doVerify = async () => {
      if (!token) {
        setVerificationStatus("error")
        dispatch(addNotification({
          type: "error",
          title: "Verification Error",
          message: "No verification token provided",
        }))
        return
      }

      try {
        const result = await verifyEmail(token).unwrap()
        // { status, message, user_id, email }

        dispatch(addNotification({
          type: "success",
          title: "Email Verified",
          message: result.message,
        }))

        setVerificationStatus("success")

        setTimeout(() => {
          navigate("/new-workspace")
        }, 1500)

      } catch (error) {
        setVerificationStatus("error")
        const msg = error.data?.message || error.message ||
                    "Email verification failed. Please try again."
        dispatch(addNotification({
          type: "error",
          title: "Verification Failed",
          message: msg,
        }))
      }
    }

    doVerify()
  }, [token, navigate, dispatch, verifyEmail])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <LogoComponent className="mx-auto h-12 w-auto" />
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Email Verification
          </h2>
        </div>

        <div className="mt-8 space-y-6">
          {verificationStatus === "verifying" && (
            <div className="text-center">
              <img
                src={GlowLoading}
                alt="Loading..."
                className="mx-auto h-12 w-12"
              />
              <p className="mt-4 text-gray-600">
                Verifying your email and logging you in…
              </p>
            </div>
          )}

          {verificationStatus === "success" && (
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <p className="mt-4 text-gray-600">
                Email verified! Redirecting to workspace creation…
              </p>
            </div>
          )}

          {verificationStatus === "error" && (
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <svg
                  className="h-6 w-6 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <p className="mt-4 text-red-600">
                Verification failed. Please request a new link or contact support.
              </p>
              <button
                onClick={() => navigate("/login")}
                className="mt-4 text-[#352090] hover:underline"
              >
                Return to Login
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default VerifyEmail
