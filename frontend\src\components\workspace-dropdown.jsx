import { useState, useRef, useEffect } from "react"
import { ChevronDown, Mail, Plus } from "lucide-react"
import { useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import { useWorkspaceManager } from "./common/WorkspaceManager"
import { selectWorkspaces, selectIsSwitching, selectWorkspaceLoading } from "../store/slices/workspaceSlice"
import { useGetWorkspaceMembersBatchQuery } from "../store/api/teamApi"

const WorkspaceDropdown = ({ adminName, adminEmail }) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef(null)
  const navigate = useNavigate()

  // Get workspace data and state from Redux
  const workspaces = useSelector(selectWorkspaces)
  const isSwitching = useSelector(selectIsSwitching)
  const { workspaces: isWorkspacesLoading } = useSelector(selectWorkspaceLoading)

  // Get workspace manager functions
  const { switchWorkspace, getWorkspaceDomainInfo } = useWorkspaceManager()

  // Get workspace members data
  const { data: workspaceMembersData } = useGetWorkspaceMembersBatchQuery(
    { workspace_ids: workspaces.map(ws => ws.id || ws._id) },
    { skip: workspaces.length === 0 }
  )

  // Close dropdown when clicking outside
  useEffect(() => {
    const onClick = (e) => {
      if (dropdownRef.current && !dropdownRef.current.contains(e.target)) {
        setIsOpen(false)
      }
    }
    document.addEventListener("mousedown", onClick)
    return () => document.removeEventListener("mousedown", onClick)
  }, [])

  const handleWorkspaceSwitch = async (workspaceId) => {
    if (isSwitching) return
    try {
      await switchWorkspace(workspaceId)
      setIsOpen(false)
    } catch (error) {
      console.error("Failed to switch workspace:", error)
    }
  }

  const handleAddMore = () => {
    navigate("/create-workspace")
    setIsOpen(false)
  }

  const isLoading = isWorkspacesLoading || isSwitching

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Trigger */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-3 hover:bg-gray-50 rounded-lg p-2 transition-colors"
        disabled={isLoading}
      >
        <span className="hidden sm:inline-block text-sm font-medium">{adminName}</span>
        <ChevronDown
          className={`w-4 h-4 text-gray-500 transition-transform ${isOpen ? "rotate-180" : ""} ${
            isLoading ? "animate-spin" : ""
          }`}
        />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-[249px] h-[244px] bg-white rounded-lg shadow-lg border border-[#EBEBEB] z-50 flex flex-col">
          {/* Admin Info */}
          <div className="px-4 pt-4 flex flex-col items-center">
            <span className="font-['Noto Sans'] font-semibold text-[18px] leading-[20px] text-black">
              {adminName}
            </span>
            <div className="mt-1 flex items-center text-[14px] leading-[20px] text-gray-500">
              <Mail className="w-4 h-4 mr-2" />
              <span className="truncate">{adminEmail}</span>
            </div>
          </div>

          {/* Divider */}
          <div className="mt-8 border-t border-[#EBEBEB] mx-4" />

          {/* Legend-style Switch workspace */}
          <div className="relative my-0.5 px-4 h-0">
            <span className="absolute left-1/2 -top-2 transform -translate-x-1/2 bg-white px-2 font-['Noto Sans'] font-medium text-[12px] leading-[12px] text-[#A4A4A4] text-center tracking-normal">
              Switch workspace
            </span>
          </div>

          {/* Workspace List */}
          <div className="flex-1 overflow-y-auto px-3 space-y-2 mt-2.5">
            {workspaces && workspaces.length > 0 ? (
              workspaces.map((workspace) => {
                const id = workspace.id || workspace._id
                const domain = getWorkspaceDomainInfo(workspace)
                const members = workspaceMembersData?.data?.[id] || []

                return (
                  <button
                    key={id}
                    onClick={() => handleWorkspaceSwitch(id)}
                    disabled={isSwitching}
                    className={`w-full py-2 rounded-[8px] font-['Noto Sans'] text-[14px] leading-[20px] font-medium transition-colors flex items-center justify-center
                      ${isSwitching
                        ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                        : "bg-[#F4F4F4] text-gray-700 hover:bg-gray-100"
                      }`}
                  >
                    {isSwitching ? (
                      <img src="/Glow loading.gif" alt="Switching…" className="w-6 h-6" />
                    ) : (
                      domain.replace(/^sc-domain:/, "")
                    )}
                  </button>
                )
              })
            ) : (
              <div className="text-sm text-gray-500 text-center">No workspaces available</div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-[#EBEBEB] mx-4" />
          <div className="px-4 py-3">
            <button
              onClick={handleAddMore}
              disabled={isSwitching}
              className="w-full font-['Noto Sans'] font-medium text-[14px] leading-[20px] text-[#352090] hover:underline"
            >
              + Add More
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default WorkspaceDropdown
