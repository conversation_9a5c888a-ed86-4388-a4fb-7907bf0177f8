import { createSlice } from "@reduxjs/toolkit"
import { authApi } from "../api/authApi"

const initialState = {
  user: null,
  isAuthenticated: false,
  loading: false,
  error: null,
  signupSuccess: false,
  passwordResetSuccess: false,
}

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    logout: (state) => {
      state.user = null
      state.isAuthenticated = false
      state.signupSuccess = false
      state.passwordResetSuccess = false,
      // Clear localStorage
      localStorage.removeItem("onboarding_state")
      localStorage.removeItem("currentWorkspaceId")
      localStorage.removeItem("userData")
    },
    clearError: (state) => {
      state.error = null
    },
    clearSignupSuccess: (state) => {
      state.signupSuccess = false
    },
    clearPasswordResetSuccess: (state) => {
      state.passwordResetSuccess = false
    },
    setAuthenticated: (state, action) => {
      state.isAuthenticated = action.payload
      if (action.payload.user) {
        state.user = action.payload.user
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Signup
      .addMatcher(authApi.endpoints.signup.matchPending, (state) => {
        state.loading = true
        state.error = null
      })
      .addMatcher(authApi.endpoints.signup.matchFulfilled, (state, action) => {
        state.loading = false
        state.signupSuccess = true
        state.error = null
      })
      .addMatcher(authApi.endpoints.signup.matchRejected, (state, action) => {
        state.loading = false
        state.error = action.payload?.data?.message  || "Signup failed. Please try again."
      })

      // Login
      .addMatcher(authApi.endpoints.login.matchPending, (state) => {
        state.loading = true
        state.error = null
      })
      .addMatcher(authApi.endpoints.login.matchFulfilled, (state, action) => {
        state.loading = false
        state.isAuthenticated = true
        state.user = action.payload.user || action.payload
        state.error = null
      })
      .addMatcher(authApi.endpoints.login.matchRejected, (state, action) => {
        state.loading = false
        state.isAuthenticated = false
        state.user = null

        // Handle specific error cases
        if (action.payload?.status === 401 && action.payload?.data?.detail?.includes("Email not verified")) {
          state.error = action.payload.data.detail
        } else {
          state.error = action.payload?.data?.detail || "Login failed. Please try again."
        }
      })

      // Password Reset (combined)
      .addMatcher(authApi.endpoints.passwordReset.matchPending, (state) => {
        state.loading = true
        state.error = null
        state.passwordResetSuccess = false
      })
      .addMatcher(authApi.endpoints.passwordReset.matchFulfilled, (state) => {
        state.loading = false
        state.passwordResetSuccess = true
        state.error = null
      })
      .addMatcher(authApi.endpoints.passwordReset.matchRejected, (state, action) => {
        state.loading = false
        state.passwordResetSuccess = false
        state.error = action.payload?.data?.detail || "Failed to send reset email. Please try again."
      })

      // Get User Profile
      .addMatcher(authApi.endpoints.getUserProfile.matchFulfilled, (state, action) => {
        state.user = action.payload
        state.isAuthenticated = true
      })
      .addMatcher(authApi.endpoints.getUserProfile.matchRejected, (state) => {
        // If profile fetch fails, user might not be authenticated
        state.user = null
        state.isAuthenticated = false
      })

      // Validate Session
      .addMatcher(authApi.endpoints.validateSession.matchFulfilled, (state, action) => {
        state.user = action.payload
        state.isAuthenticated = true
        state.loading = false
        state.error = null
      })
      .addMatcher(authApi.endpoints.validateSession.matchRejected, (state, action) => {
        state.user = null
        state.isAuthenticated = false
        state.loading = false
        state.error = action.error?.message || "Session validation failed"
      })

      // Logout
      .addMatcher(authApi.endpoints.logout.matchFulfilled, (state) => {
        state.user = null
        state.isAuthenticated = false
        state.signupSuccess = false
        state.passwordResetSuccess = false
      })
  },
})

export const {
  logout,
  clearError,
  clearSignupSuccess,
  clearPasswordResetSuccess,
  setAuthenticated,
} = authSlice.actions

export default authSlice.reducer
