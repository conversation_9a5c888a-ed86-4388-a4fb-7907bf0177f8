import { apiSlice } from "./apiSlice"
import { logout as logoutAction } from "../slices/authSlice" 
import { resetNavigationState } from "../slices/navigationSlice"

const USER_SERVICE_URL = import.meta.env.VITE_USER_SERVICE || "http://localhost:8000"

export const authApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Signup
    signup: builder.mutation({
      query: (userData) => ({
        url: "/auth/signup",
        method: "POST",
        body: {
          full_name: userData.fullName,
          email: userData.email,
          phone_number: userData.phoneNumber,
          password: userData.password,
        },
        baseUrl: USER_SERVICE_URL,
      }),
      transformResponse: (response) => {
        // Store onboarding state if provided
        if (response.onboarding_state) {
          localStorage.setItem("onboarding_state", JSON.stringify(response.onboarding_state))
        }
        return response
      },
    }),

    // Login
    login: builder.mutation({
      query: (credentials) => ({
        url: "/auth/login",
        method: "POST",
        body: {
          email: credentials.email,
          password: credentials.password,
          remember_me: credentials.rememberMe || false,
        },
        baseUrl: USER_SERVICE_URL,
      }),
      async onQueryStarted(credentials, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled
          console.log("Login successful:", data)

          // Store user data in localStorage if remember me is checked
          if (credentials.rememberMe) {
            localStorage.setItem(
              "userData",
              JSON.stringify({
                email: data.email,
                role: data.role,
              }),
            )
            console.log("User data stored in localStorage")
          }
        } catch (error) {
          console.error("Login failed:", error)
        }
      },
    }),

    // Check Onboarding Status
    checkOnboarding: builder.query({
      query: () => ({
        url: "/auth/check-onboarding",
        baseUrl: USER_SERVICE_URL,
      }),
      providesTags: ["Onboarding"],
    }),

    // Verify Email
    verifyEmail: builder.mutation({
      query: (token) => ({
        url: `/auth/verify`,
        method: "GET",
        params: { token },
        baseUrl: USER_SERVICE_URL,
      }),
        transformResponse: (response) => {
        // response = { status, message, user_id, email }
        return response
      },
    }),

  // Combined Password Reset
    passwordReset: builder.mutation({
      query: ({ email, token, new_password }) => ({
        url: "/auth/password-reset",
        method: "POST",
        body: { email, token, new_password },
        baseUrl: USER_SERVICE_URL,
        }),
    }),

    // Get User Profile
    getUserProfile: builder.query({
      query: () => ({
        url: "/auth/user-profile",
        baseUrl: USER_SERVICE_URL,
      }),
      providesTags: ["User"],
    }),

    // Validate Session
    validateSession: builder.query({
      query: () => ({
        url: "/session/validate",
        baseUrl: USER_SERVICE_URL,
      }),
      providesTags: ["User"],
    }),

    // Logout
    logout: builder.mutation({
      query: () => ({
        url: "/auth/logout",
        method: "POST",
        baseUrl: USER_SERVICE_URL,
      }),
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        try {
          // wait for the server to delete the session and clear the cookie
          await queryFulfilled

          // 1) Reset Redux auth state
          dispatch(logoutAction())
          // 2) Reset navigation
          dispatch(resetNavigationState())
          // 3) Clear RTK Query caches
          dispatch(clusterApi.util.resetApiState()) 
          dispatch(authApi.util.resetApiState())
          // 4) Clear any localStorage
          localStorage.clear()
        } catch {
          // Even if logout API fails, we still clear local session
          dispatch(logoutAction())
          dispatch(resetNavigationState())
          dispatch(clusterApi.util.resetApiState())
          dispatch(authApi.util.resetApiState())
          localStorage.clear()
        }
      },
      invalidatesTags: ["User", "Workspace"],
    }),
  }),
})

export const {
  useSignupMutation,
  useLoginMutation,
  useCheckOnboardingQuery,
  useVerifyEmailMutation,
  usePasswordResetMutation,
  useGetUserProfileQuery,
  useValidateSessionQuery,
  useLogoutMutation,
} = authApi
