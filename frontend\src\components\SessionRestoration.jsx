import { useEffect, useState } from "react"
import { useSelector } from "react-redux"
import { useNavigate, useLocation } from "react-router-dom"
import { useGetUserProfileQuery } from "../store/api/authApi"
import { useGetUserWorkspacesQuery } from "../store/api/workspaceApi"

const SessionRestoration = ({ children }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const [isInitialized, setIsInitialized] = useState(false)
  const { isAuthenticated } = useSelector((state) => state.auth)

  // Try to get user profile to check if session is valid
  const {
    data: userProfile,
    isLoading: profileLoading,
    error: profileError,
  } = useGetUserProfileQuery(undefined, {
    // Don't skip - always try to get user profile to validate session
    skip: false,
    retry: 1,
  })

  // Get user workspaces if authenticated
  const {
    data: workspacesData,
    isLoading: workspacesLoading,
    error: workspacesError
  } = useGetUserWorkspacesQuery(undefined, {
    skip: !isAuthenticated,
    refetchOnMountOrArgChange: true
  })

  useEffect(() => {
    console.log("🔍 SessionRestoration effect:", {
      profileLoading,
      profileError: profileError?.status,
      userProfile: !!userProfile,
      isAuthenticated,
      isInitialized,
      workspacesLoading,
      workspacesCount: workspacesData?.length,
      pathname: location.pathname
    })

    if (profileLoading || (isAuthenticated && workspacesLoading)) {
      // Still loading, don't initialize yet
      return
    }

    // Profile fetch completed (either success or failure)
    // The auth slice will automatically handle the state updates through RTK Query matchers
    if (!isInitialized) {
      if (userProfile) {
        console.log("✅ Valid session restored via user profile:", userProfile.email)
        
        // If authenticated but no workspaces, redirect to create workspace
        if (isAuthenticated && !workspacesLoading) {
          const hasNoWorkspaces = !workspacesData || workspacesData.length === 0
          const isOnCreateWorkspace = location.pathname === '/new-workspace'
          const isOnVerifyEmail = location.pathname.includes('/verify-email')
          
          if (hasNoWorkspaces && !isOnCreateWorkspace && !isOnVerifyEmail) {
            console.log("⚠️ No workspaces found, redirecting to create workspace")
            navigate('/new-workspace', { replace: true })
            return // Don't set initialized yet
          }
        }
      } else if (profileError) {
        console.log("❌ Session restoration failed:", profileError.status)
      }
      setIsInitialized(true)
    }
  }, [
    profileLoading, 
    userProfile, 
    profileError, 
    isAuthenticated, 
    isInitialized,
    workspacesLoading,
    workspacesData,
    location.pathname,
    navigate
  ])

  // Show loading screen while checking session
  if (!isInitialized || profileLoading || (isAuthenticated && workspacesLoading)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#352090] mx-auto"></div>
          <p className="mt-4 text-gray-600">
            {profileLoading ? "Restoring your session..." :
             workspacesLoading ? "Loading your workspaces..." :
             "Initializing..."}
          </p>
          <p className="mt-2 text-sm text-gray-500">Please wait...</p>
        </div>
      </div>
    )
  }

  return children
}

export default SessionRestoration
