import { createSlice } from "@reduxjs/toolkit"

const initialState = {
  currentStep: "google_auth", // Only workspace-specific steps: google_auth, site_selection
  isCompleted: false,
  workspaceId: null,
  workspaceStates: {}, // Map of workspace IDs to their onboarding states
}

const onboardingSlice = createSlice({
  name: "onboarding",
  initialState,
  reducers: {
    setCurrentStep: (state, action) => {
      state.currentStep = action.payload
    },

    setWorkspaceId: (state, action) => {
      state.workspaceId = action.payload
    },

    updateWorkspaceState: (state, action) => {
      const { workspaceId, onboardingState } = action.payload
      if (workspaceId) {
        state.workspaceStates[workspaceId] = {
          ...state.workspaceStates[workspaceId],
          ...onboardingState
        }
      }
    },

    completeOnboarding: (state, action) => {
      const { workspaceId } = action.payload
      if (workspaceId && state.workspaceStates[workspaceId]) {
        state.workspaceStates[workspaceId].isCompleted = true
        state.workspaceStates[workspaceId].currentStep = "completed"
      }
      // Also update global state if it's the current workspace
      if (workspaceId === state.workspaceId) {
        state.isCompleted = true
        state.currentStep = "completed"
      }
    },

    resetOnboarding: (state, action) => {
      const { workspaceId } = action.payload || {}
      if (workspaceId) {
        // Reset specific workspace
        delete state.workspaceStates[workspaceId]
        if (workspaceId === state.workspaceId) {
          state.currentStep = "google_auth"
          state.isCompleted = false
        }
      } else {
        // Reset all
        return initialState
      }
    }
  }
})

export const { 
  setCurrentStep, 
  setWorkspaceId, 
  updateWorkspaceState,
  completeOnboarding, 
  resetOnboarding 
} = onboardingSlice.actions

// Selectors
export const selectOnboardingState = (state) => ({
  currentStep: state.onboarding.currentStep,
  isCompleted: state.onboarding.isCompleted,
  workspaceId: state.onboarding.workspaceId
})

export const selectWorkspaceOnboardingState = (workspaceId) => (state) => 
  state.onboarding.workspaceStates[workspaceId]

export default onboardingSlice.reducer
