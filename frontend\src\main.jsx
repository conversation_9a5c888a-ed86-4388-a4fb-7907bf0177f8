import React from "react"
import <PERSON>act<PERSON><PERSON> from "react-dom/client"
import { Provider } from "react-redux"
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from "react-router-dom"
import { PersistGate } from "redux-persist/integration/react"
import { store, persistor } from "./store/store"
import App from "./App"
import "./index.css"
import NotificationContainer from "./components/common/NotificationContainer"

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <BrowserRouter>
          <App />
          <NotificationContainer />
        </BrowserRouter>
      </PersistGate>
    </Provider>
  </React.StrictMode>
)
