import { useDispatch, useSelector } from "react-redux"
import { useNavigate, useLocation } from "react-router-dom"
import { 
  setWorkspaceSwitchingState,
  selectWorkspaces,
  selectIsSwitching,
  selectCurrentWorkspace
} from "../../store/slices/workspaceSlice"
import { 
  handleWorkspaceSwitch,
  setNavigationState 
} from "../../store/slices/navigationSlice"
import { addNotification } from "../../store/slices/uiSlice"
import { 
  workspaceApi,
  useUpdateLastAccessedWorkspaceMutation 
} from "../../store/api/workspaceApi"
import { onboardingApi } from "../../store/api/onboardingApi"

class WorkspaceManager {
  constructor(dispatch, navigate, location, updateLastAccessed) {
    this.dispatch = dispatch
    this.navigate = navigate
    this.location = location
    this.updateLastAccessed = updateLastAccessed
  }

  // Get workspace domain info
  getWorkspaceDomainInfo = (workspace) => {
    if (!workspace) return "No domain set"
    
    if (workspace.domain_info?.siteUrl) {
      return workspace.domain_info.siteUrl
    }
    
    return workspace.has_domain === false ? "No domain set" : "Loading domain..."
  }

  // Switch workspace
  switchWorkspace = async (workspaceId, workspaces) => {
    if (!workspaceId || typeof workspaceId !== "string") {
      throw new Error(`Invalid workspace ID: ${workspaceId}`)
    }

    // Validate workspace exists
    const workspaceExists = workspaces.some(
      (ws) => (ws.id || ws._id) === workspaceId
    )
    if (!workspaceExists) {
      throw new Error(`Workspace not found: ${workspaceId}`)
    }

    // Set switching state
    this.dispatch(setWorkspaceSwitchingState(true))
    this.dispatch(setNavigationState({
      isNavigating: true,
      navigationType: "inter-workspace"
    }))

    try {
      // Update last accessed workspace
      const result = await this.updateLastAccessed(workspaceId).unwrap()
      console.log("Switch workspace response:", result)

      if (result.status !== "success") {
        throw new Error(result.message || "Failed to switch workspace")
      }

      // Update navigation state
      this.dispatch(handleWorkspaceSwitch({
        newWorkspaceId: result.workspace_id,
        currentPath: this.location.pathname
      }))

      // Invalidate caches
      this.dispatch(
        onboardingApi.util.invalidateTags([
          { type: "Onboarding", id: workspaceId }
        ])
      )

      // Navigate to workspace
      this.navigate(`/workspace/${result.workspace_id}`, {
        replace: true,
        state: {
          forceRemount: Date.now(),
          workspaceSwitch: true
        }
      })

      // Show success notification with domain info if available
      const domainMessage = result.domain_info?.siteUrl 
        ? ` with domain: ${result.domain_info.siteUrl}`
        : ""

      this.dispatch(
        addNotification({
          type: "success",
          title: "Workspace Switched",
          message: `Switched to workspace successfully${domainMessage}`,
        })
      )

      return result

    } catch (error) {
      console.error("Workspace switch error:", error)
      this.dispatch(
        addNotification({
          type: "error",
          title: "Switch Failed",
          message: error.message || "Failed to switch workspace",
        })
      )
      throw error

    } finally {
      // Clear navigation state
      this.dispatch(setWorkspaceSwitchingState(false))
      this.dispatch(setNavigationState({
        isNavigating: false,
        navigationType: null
      }))
    }
  }

  // Create workspace
  createWorkspace = async (workspaceName) => {
    try {
      const result = await this.dispatch(
        workspaceApi.endpoints.createWorkspace.initiate({ name: workspaceName })
      ).unwrap()

      const workspaceId = result.id || result._id

      // Update session
      await this.switchWorkspace(workspaceId, [result])

      return workspaceId

    } catch (error) {
      this.dispatch(
        addNotification({
          type: "error",
          title: "Creation Failed",
          message: error.message || "Failed to create workspace",
        })
      )
      throw error
    }
  }
}

// Hook for using WorkspaceManager
export const useWorkspaceManager = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const location = useLocation()
  
  // Use specific selectors
  const workspaces = useSelector(selectWorkspaces)
  const isSwitching = useSelector(selectIsSwitching)
  const currentWorkspace = useSelector(selectCurrentWorkspace)

  // Get the mutation hook
  const [updateLastAccessed] = useUpdateLastAccessedWorkspaceMutation()

  const manager = new WorkspaceManager(dispatch, navigate, location, updateLastAccessed)

  return {
    getWorkspaceDomainInfo: manager.getWorkspaceDomainInfo,
    switchWorkspace: (workspaceId) => 
      manager.switchWorkspace(workspaceId, workspaces),
    createWorkspace: manager.createWorkspace,
    isSwitching,
    currentWorkspace
  }
}

export default WorkspaceManager 