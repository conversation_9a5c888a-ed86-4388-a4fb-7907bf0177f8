import { createSlice } from "@reduxjs/toolkit"
import { workspace<PERSON>pi } from "../api/workspaceApi"

const initialState = {
  // List of all workspaces
  workspaces: [],
  
  // Current workspace
  currentWorkspace: null,
  
  // Operation states
  isSwitching: false,
  isCreating: false,
  
  // Loading states
  loading: {
    workspaces: false
  },
  
  // Error state
  error: null
}

const workspaceSlice = createSlice({
  name: "workspace",
  initialState,
  reducers: {
    setCurrentWorkspace: (state, action) => {
      state.currentWorkspace = action.payload
    },

    setWorkspaceSwitchingState: (state, action) => {
      state.isSwitching = action.payload
    },
    
    setWorkspaceCreatingState: (state, action) => {
      state.isCreating = action.payload
    },
    
    clearError: (state) => {
      state.error = null
    },
    
    resetWorkspaceState: () => initialState
  },
  
  extraReducers: (builder) => {
    // Get workspaces
    builder
      .addMatcher(
        workspaceApi.endpoints.getUserWorkspaces.matchPending,
        (state) => {
          state.loading.workspaces = true
          state.error = null
        }
      )
      .addMatcher(
        workspaceApi.endpoints.getUserWorkspaces.matchFulfilled,
        (state, action) => {
          state.workspaces = action.payload
          state.loading.workspaces = false
          state.error = null

          // Set current workspace if none is set
          if (!state.currentWorkspace && action.payload.length > 0) {
            state.currentWorkspace = action.payload[0].id || action.payload[0]._id
          }
        }
      )
      .addMatcher(
        workspaceApi.endpoints.getUserWorkspaces.matchRejected,
        (state, action) => {
          state.loading.workspaces = false
          state.error = action.error.message
        }
      )
      
    // Update last accessed workspace
    builder
      .addMatcher(
        workspaceApi.endpoints.updateLastAccessedWorkspace.matchFulfilled,
        (state, action) => {
          const updatedWorkspace = action.payload.workspace
          if (updatedWorkspace) {
            const index = state.workspaces.findIndex(
              ws => (ws.id || ws._id) === updatedWorkspace.id
            )
            if (index !== -1) {
              state.workspaces[index] = {
                ...state.workspaces[index],
                ...updatedWorkspace,
                domain_info: updatedWorkspace.domain_info,
                has_domain: updatedWorkspace.has_domain !== undefined 
                  ? updatedWorkspace.has_domain 
                  : (updatedWorkspace.domain_info ? true : false)
              }
            }
          }
        }
      )
      
    // Create workspace
    builder
      .addMatcher(
        workspaceApi.endpoints.createWorkspace.matchPending,
        (state) => {
          state.isCreating = true
          state.error = null
        }
      )
      .addMatcher(
        workspaceApi.endpoints.createWorkspace.matchFulfilled,
        (state, action) => {
          state.workspaces.push(action.payload)
          state.isCreating = false
          state.error = null
        }
      )
      .addMatcher(
        workspaceApi.endpoints.createWorkspace.matchRejected,
        (state, action) => {
          state.isCreating = false
          state.error = action.error.message
        }
      )
  }
})

export const {
  setCurrentWorkspace,
  setWorkspaceSwitchingState,
  setWorkspaceCreatingState,
  clearError,
  resetWorkspaceState
} = workspaceSlice.actions

// Selectors
export const selectWorkspaces = (state) => state.workspace.workspaces
export const selectCurrentWorkspace = (state) => state.workspace.currentWorkspace
export const selectWorkspaceById = (workspaceId) => (state) => 
  state.workspace.workspaces.find(ws => (ws.id || ws._id) === workspaceId)
export const selectWorkspaceLoading = (state) => state.workspace.loading
export const selectWorkspaceError = (state) => state.workspace.error
export const selectIsSwitching = (state) => state.workspace.isSwitching
export const selectIsCreating = (state) => state.workspace.isCreating

export default workspaceSlice.reducer
