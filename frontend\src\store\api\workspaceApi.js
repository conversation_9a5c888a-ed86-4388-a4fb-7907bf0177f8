import { apiSlice } from "./apiSlice"

const USER_SERVICE_URL = import.meta.env.VITE_USER_SERVICE || "http://localhost:8000"

// Define tag types for cache invalidation
export const WORKSPACE_TAG_TYPES = {
  WORKSPACE: "Workspace",
  WORKSPACE_SETTINGS: "WorkspaceSettings",
  WORKSPACE_DOMAIN: "WorkspaceDomain"
}

// Error messages
const ERROR_MESSAGES = {
  FETCH_WORKSPACES: "Failed to fetch workspaces. Please try again.",
  UPDATE_ACCESS: "Failed to update workspace access.",
  CREATE_WORKSPACE: "Failed to create workspace.",
  UPDATE_WORKSPACE: "Failed to update workspace.",
  DELETE_WORKSPACE: "Failed to delete workspace."
}

export const workspaceApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get user workspaces
    getUserWorkspaces: builder.query({
      query: () => ({
        url: "/workspaces",
        baseUrl: USER_SERVICE_URL,
      }),
      providesTags: (result = []) => [
        WORKSPACE_TAG_TYPES.WORKSPACE,
        ...result.map(({ id, _id }) => ({
          type: WORKSPACE_TAG_TYPES.WORKSPACE,
          id: id || _id
        }))
      ],
      transformResponse: (response) => {
        // Normalize response format
        let workspaces = Array.isArray(response) 
          ? response 
          : response?.workspaces || response?.data || []

        // Validate and normalize workspace objects
        return workspaces
          .filter(workspace => workspace && (workspace.id || workspace._id))
          .map(workspace => ({
            ...workspace,
            id: workspace.id || workspace._id,
            domain_info: workspace.domain_info || null,
            has_domain: workspace.has_domain ?? (workspace.domain_info ? true : false)
          }))
      },
      transformErrorResponse: (response) => ({
        status: response.status,
        message: ERROR_MESSAGES.FETCH_WORKSPACES
      })
    }),

    // Update last accessed workspace
    updateLastAccessedWorkspace: builder.mutation({
      query: (workspaceId) => ({
        url: `/workspaces/workspace/${workspaceId}/access`,
        method: "GET",
        baseUrl: USER_SERVICE_URL,
      }),
      invalidatesTags: (result, error, workspaceId) => [
        { type: WORKSPACE_TAG_TYPES.WORKSPACE, id: workspaceId },
        WORKSPACE_TAG_TYPES.WORKSPACE_DOMAIN
      ],
      // Transform response to match new structure
      transformResponse: (response) => {
        if (!response || response.status !== "success") {
          throw new Error(response?.message || "Failed to update workspace access")
        }

        return {
          status: response.status,
          message: response.message,
          workspace_id: response.workspace_id,
          workspace_name: response.workspace_name,
          domain_info: response.domain_info || null
        }
      },
      transformErrorResponse: (response) => ({
        status: response.status,
        message: ERROR_MESSAGES.UPDATE_ACCESS
      })
    }),

    // Create workspace
    createWorkspace: builder.mutation({
      query: (workspaceData) => ({
        url: "/workspaces",
        method: "POST",
        body: workspaceData,
        baseUrl: USER_SERVICE_URL,
      }),
      invalidatesTags: [WORKSPACE_TAG_TYPES.WORKSPACE],
      transformResponse: (response) => ({
        ...response,
        id: response.id || response._id,
        domain_info: null,
        has_domain: false
      }),
      transformErrorResponse: (response) => ({
        status: response.status,
        message: ERROR_MESSAGES.CREATE_WORKSPACE
      })
    }),

    // Update workspace
    updateWorkspace: builder.mutation({
      query: ({ workspaceId, ...workspaceData }) => ({
        url: `/workspaces/workspace/${workspaceId}`,
        method: "PUT",
        body: workspaceData,
        baseUrl: USER_SERVICE_URL,
      }),
      invalidatesTags: (result, error, { workspaceId }) => [
        { type: WORKSPACE_TAG_TYPES.WORKSPACE, id: workspaceId },
        WORKSPACE_TAG_TYPES.WORKSPACE_SETTINGS
      ],
      transformResponse: (response) => ({
        ...response,
        id: response.id || response._id
      }),
      transformErrorResponse: (response) => ({
        status: response.status,
        message: ERROR_MESSAGES.UPDATE_WORKSPACE
      })
    }),

    // Delete workspace
    deleteWorkspace: builder.mutation({
      query: (workspaceId) => ({
        url: `/workspaces/workspace/${workspaceId}`,
        method: "DELETE",
        baseUrl: USER_SERVICE_URL,
      }),
      invalidatesTags: (result, error, workspaceId) => [
        { type: WORKSPACE_TAG_TYPES.WORKSPACE, id: workspaceId },
        WORKSPACE_TAG_TYPES.WORKSPACE
      ],
      transformErrorResponse: (response) => ({
        status: response.status,
        message: ERROR_MESSAGES.DELETE_WORKSPACE
      })
    })
  }),
  overrideExisting: false
})

export const {
  useGetUserWorkspacesQuery,
  useUpdateLastAccessedWorkspaceMutation,
  useCreateWorkspaceMutation,
  useUpdateWorkspaceMutation,
  useDeleteWorkspaceMutation
} = workspaceApi
