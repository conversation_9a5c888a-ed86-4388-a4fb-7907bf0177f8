import { useState, useEffect } from "react"
import { useNavigate, useSearchParams } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import LogoComponent from "../components/logo-component"
import SpiralImage from "../assets/image.png"
import { usePasswordResetMutation } from "../store/api/authApi"
import { clearError, clearPasswordResetSuccess } from "../store/slices/authSlice"
import { addNotification } from "../store/slices/uiSlice"

const ResetPassword = () => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const dispatch = useDispatch()

  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [token, setToken] = useState(null)
  const [tokenError, setTokenError] = useState(null)

  const { loading, error, passwordResetSuccess } = useSelector((state) => state.auth)
  const [passwordReset] = usePasswordResetMutation()

  // Extract token from URL and validate
  useEffect(() => {
    const urlToken = searchParams.get("token")
    if (!urlToken) {
      setTokenError("Invalid reset link. Please request a new password reset.")
      return
    }
    setToken(urlToken)
  }, [searchParams])

  // Clear error and success state when component mounts
  useEffect(() => {
    dispatch(clearError())
    dispatch(clearPasswordResetSuccess())
  }, [dispatch])

  // Handle successful password reset
  useEffect(() => {
    if (passwordResetSuccess) {
      dispatch(
        addNotification({
          type: "success",
          message: "Password reset successful! Redirecting to login...",
        }),
      )

      // Redirect to login after 3 seconds
      const timer = setTimeout(() => {
        navigate("/login")
      }, 3000)

      return () => clearTimeout(timer)
    }
  }, [passwordResetSuccess, navigate, dispatch])

  const handleSubmit = async (e) => {
    e.preventDefault()
    dispatch(clearError())

    if (newPassword !== confirmPassword) {
      dispatch(
        addNotification({
          type: "error",
          message: "Passwords do not match",
        }),
      )
      return
    }

    if (newPassword.length < 6) {
      dispatch(
        addNotification({
          type: "error",
          message: "Password must be at least 6 characters long",
        }),
      )
      return
    }

    try {
      await passwordReset({
        token,
        new_password: newPassword,
      }).unwrap()
    } catch (error) {
      console.error("Reset password error:", error)

      dispatch(
        addNotification({
          type: "error",
          message: error.data?.detail || "Failed to reset password. Please try again.",
        }),
      )
    }
  }

  // Show token error
  if (tokenError) {
    return (
      <div className="flex flex-col md:flex-row min-h-screen w-full font-noto">
        <div className="w-full md:w-1/2 flex items-center justify-center p-4 sm:p-6 md:p-8 lg:p-10 overflow-auto">
          <div className="w-full max-w-[426px]">
            <div className="mb-6">
              <LogoComponent />
            </div>
            <div className="text-center">
              <h1 className="text-[30px] font-bold mb-2 text-red-600">Invalid Reset Link</h1>
              <p className="text-[14px] text-gray-600 mb-6">{tokenError}</p>
              <button
                onClick={() => navigate("/forgot-password")}
                className="w-full h-[52px] bg-[#352090] text-white text-sm rounded-[12px] hover:bg-[#2a1a70] transition-colors duration-300"
              >
                Request New Reset Link
              </button>
            </div>
          </div>
        </div>
        <div className="hidden md:block md:w-1/2 relative">
          <div className="absolute inset-0 m-5">
            <img
              src={SpiralImage || "/placeholder.svg"}
              alt="Decorative spiral"
              className="w-full h-full object-cover rounded-[40px]"
            />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col md:flex-row min-h-screen w-full font-noto">
      {/* Form Section */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-4 sm:p-6 md:p-8 lg:p-10 overflow-auto">
        <div className="w-full max-w-[426px]">
          {/* Logo */}
          <div className="mb-6">
            <LogoComponent />
          </div>

          {!passwordResetSuccess ? (
            <>
              <h1 className="text-[30px] font-bold mb-2">Reset Password</h1>
              <p className="text-[14px] text-gray-600 mb-6">Please enter your new password.</p>

              <form onSubmit={handleSubmit}>
                <div className="mb-5">
                  <label htmlFor="newPassword" className="block text-[14px] mb-2">
                    New Password
                  </label>
                  <input
                    type="password"
                    id="newPassword"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="w-full h-[48px] px-3 py-2 text-sm bg-white border border-[#D9D9D9] rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
                    required
                    placeholder="Enter new password"
                    disabled={loading}
                    minLength={6}
                  />
                </div>

                <div className="mb-5">
                  <label htmlFor="confirmPassword" className="block text-[14px] mb-2">
                    Confirm Password
                  </label>
                  <input
                    type="password"
                    id="confirmPassword"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="w-full h-[48px] px-3 py-2 text-sm bg-white border border-[#D9D9D9] rounded-[12px] focus:outline-none focus:ring-1 focus:ring-[#352090]"
                    required
                    placeholder="Confirm new password"
                    disabled={loading}
                    minLength={6}
                  />
                </div>

                {error && (
                  <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md text-red-600">{error}</div>
                )}

                <button
                  type="submit"
                  disabled={loading || !token}
                  className="w-full h-[52px] bg-[#352090] text-white text-sm rounded-[12px] hover:bg-[#2a1a70] transition-colors duration-300 disabled:opacity-50 flex items-center justify-center"
                >
                  {loading ? (
                    <>
                      <svg
                        className="animate-spin h-4 w-4 mr-2 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z" />
                      </svg>
                      Resetting...
                    </>
                  ) : (
                    "Reset Password"
                  )}
                </button>
              </form>
            </>
          ) : (
            <div className="text-center">
              <h1 className="text-[30px] font-bold mb-2">Password Reset Successful</h1>
              <p className="text-[14px] text-gray-600 mb-6">
                Your password has been reset successfully. You will be redirected to the login page.
              </p>
              <div className="flex items-center justify-center">
                <svg
                  className="animate-spin h-4 w-4 mr-2 text-[#352090]"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z" />
                </svg>
                <span className="text-[#352090]">Redirecting...</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Image Section */}
      <div className="hidden md:block md:w-1/2 relative">
        <div className="absolute inset-0 m-5">
          <img
            src={SpiralImage || "/placeholder.svg"}
            alt="Decorative spiral"
            className="w-full h-full object-cover rounded-[40px]"
          />
        </div>
      </div>
    </div>
  )
}

export default ResetPassword
