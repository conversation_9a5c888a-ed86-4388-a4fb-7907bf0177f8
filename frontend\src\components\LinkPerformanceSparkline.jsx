// src/components/LinkPerformanceSparkline.jsx
import { memo, forwardRef } from "react"
import { useGetLinkPerformanceQuery } from "../store/api/linkApi"
import { Loader } from "lucide-react"
import { getComparisonDates } from "../utils/dateUtils"
import Tippy from "@tippyjs/react"
import "tippy.js/dist/tippy.css"   

const MetricDisplay = ({ label, value, color }) => (
  <div className="flex flex-col items-center">
    <span className="text-xs text-gray-500">{label}</span>
    <span className="text-base font-semibold" style={{ color }}>
      {value}
    </span>
  </div>
)

// Create a forwardRef wrapper for the arrow icon
const ArrowIcon = forwardRef(({ src, alt, className, ...props }, ref) => (
  <img
    ref={ref}
    src={src}
    alt={alt}
    className={`${className} cursor-pointer`}
    {...props}
  />
))

ArrowIcon.displayName = "ArrowIcon"

const LinkPerformanceSparkline = memo(({
  linkId,
  clusterId,
  metricType = "clicks",
  startDate: providedStartDate,
  endDate: providedEndDate,
  isLoading: externalLoading = false
}) => {
  const skipQuery = !linkId || !clusterId || !providedStartDate || !providedEndDate

  const {
    data: totals,
    isLoading: queryLoading,
    error,
  } = useGetLinkPerformanceQuery(
    { linkId, clusterId, startDate: providedStartDate, endDate: providedEndDate },
    { skip: skipQuery, pollingInterval: 300_000 }
  )

  if (externalLoading || queryLoading) {
    return (
      <div className="flex items-center justify-center text-amber-600">
        <Loader className="w-5 h-5 animate-spin" />
      </div>
    )
  }

  if (error || !totals || typeof totals[metricType] === "undefined") {
    return (
      <div className="flex items-center justify-center text-xs text-gray-400">
        No data
      </div>
    )
  }

  const displayValue = totals.formatted?.[metricType] ?? String(totals[metricType])

  // figure out the comparison dates
  const { compare_start_date: compareStart, compare_end_date: compareEnd } =
    getComparisonDates(providedStartDate, providedEndDate)

  // if backend returned a comparison block, pull that too
  const comp = totals.comparison
  const compValue = comp?.formatted?.[metricType] ?? (comp ? String(comp[metricType]) : null)

  // decide arrow direction: up if current > comparison, down otherwise
  const isUp = comp && totals[metricType] > comp[metricType]
  const arrowIcon = isUp ? "/performance_up.svg" : "/performance_down.svg"
  const arrowAlt  = isUp ? "Up" : "Down"

  const metricProps = {
    clicks:      { color: "#4285F4", label: "Clicks"},
    impressions: { color: "#5E34B2", label: "Impressions"},
  }[metricType] || { color: "#000"}

  return (
    <div className="flex items-start justify-start gap-1">
      <MetricDisplay
        value={displayValue}
        color={metricProps.color}
      />
      {comp && (
        <Tippy
          content={
            <>
              <div className="text-xs leading-tight">
                <strong>Current:</strong> {providedStartDate} → {providedEndDate}<br/>
                {metricProps.label}: {displayValue}
              </div>
              <div className="mt-1 text-xs leading-tight">
                <strong>Compare:</strong> {compareStart} → {compareEnd}<br/>
                {metricProps.label}: {compValue}
              </div>
            </>
          }
          arrow={true}
          placement="top"
          delay={[200, 100]}
        >
          <ArrowIcon
            src={arrowIcon}
            alt={arrowAlt}
            className="w-4 h-6"
          />
        </Tippy>
      )}
    </div>
  )
})

LinkPerformanceSparkline.displayName = "LinkPerformanceSparkline"

export default LinkPerformanceSparkline
