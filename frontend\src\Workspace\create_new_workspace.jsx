// src/pages/CreateNewWorkspace.jsx
import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"

import Navbar from "../components/navbar"
import CreateWorkspaceModal from "./create-workspace-modal"
import { useCreateWorkspaceMutation } from "../store/api/workspaceApi"
import { addNotification } from "../store/slices/uiSlice"
import { selectIsCreating } from "../store/slices/workspaceSlice"

const CreateNewWorkspace = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const navigate = useNavigate()
  const dispatch = useDispatch()

  // RTK‑Query mutation + loading flag from the slice
  const [createWorkspace] = useCreateWorkspaceMutation()
  const isCreating = useSelector(selectIsCreating)

  const openModal = () => setIsModalOpen(true)
  const closeModal = () => setIsModalOpen(false)

  const handleModalSubmit = async (workspaceName) => {
    try {
      // 1) Call the mutation with the required shape
      const { id } = await createWorkspace({ name: workspaceName }).unwrap()

      // 2) Fire success toast
      dispatch(
        addNotification({
          type: "success",
          message: "Workspace created successfully!",
        })
      )

      // 3) Close modal and navigate into the new workspace
      closeModal()
      navigate(`/workspace/${id}`)
      return true

    } catch (err) {
      console.error("Error creating workspace:", err)

      // 4) Fire error toast
      dispatch(
        addNotification({
          type: "error",
          message: err.data?.message || "Failed to create workspace.",
        })
      )
      return false
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 relative">
      {/* Navbar */}
      <div className="fixed top-0 left-0 right-0 h-16 bg-white z-30">
        <Navbar />
      </div>

      {/* Main Content */}
      <main
        className="flex items-center justify-center pt-20"
        style={{ minHeight: "calc(100vh - 64px)" }}
      >
        <button
          onClick={openModal}
          disabled={isCreating}
          className="flex items-center gap-[9px] px-6 py-3 cursor-pointer transition-opacity duration-200 focus:outline-none focus:ring-2 focus:ring-[#352090] focus:ring-offset-2 rounded-lg hover:opacity-80"
          style={{ width: "218px", height: "45px" }}
        >
          {/* plus icon SVG */}
          <svg
            width="45"
            height="45"
            viewBox="0 0 45 45"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="flex-shrink-0"
          >
            <rect width="45" height="45" rx="14" fill="#E4EEFF" />
            <path
              d="M22.5 17.5417V27.4584M17.5417 22.5001H27.4584"
              stroke="#352090"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="text-[#352090] text-[14px] leading-[100%] tracking-[0%] whitespace-nowrap font-['Poppins']">
            Create New Workspace
          </span>
        </button>
      </main>

      {/* Modal */}
      <CreateWorkspaceModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onSubmit={handleModalSubmit}
        loading={isCreating}
      />
    </div>
  )
}

export default CreateNewWorkspace
